import { POST } from './route';
import { NextRequest } from 'next/server';
import { auth } from '@/lib';
import { getLLMService } from '@/backend/wire';
import { mockGrammarPracticeParams, mockGrammarPracticeResult } from '@/test/fixtures';
import { Language, Difficulty } from '@prisma/client';

// Mock dependencies
jest.mock('@/lib', () => ({
	auth: jest.fn(),
}));

jest.mock('@/backend/wire', () => ({
	getLLMService: jest.fn(),
}));

jest.mock('fs/promises', () => ({
	readFile: jest.fn(),
	writeFile: jest.fn(),
	mkdir: jest.fn(),
}));

describe('/api/llm/generate-grammar-practice', () => {
	const mockAuth = auth as jest.MockedFunction<typeof auth>;
	const mockGetLLMService = getLLMService as jest.MockedFunction<typeof getLLMService>;
	const mockLLMService = {
		generateGrammarPractice: jest.fn(),
	};

	beforeEach(() => {
		jest.clearAllMocks();
		mockGetLLMService.mockResolvedValue(mockLLMService as any);
		mockAuth.mockResolvedValue({
			user: { id: 'test-user-id' },
		} as any);
	});

	const createRequest = (body: any) => {
		return {
			json: jest.fn().mockResolvedValue(body),
		} as unknown as NextRequest;
	};

	describe('Authentication', () => {
		it('should return 401 when user is not authenticated', async () => {
			mockAuth.mockResolvedValue(null);
			const request = createRequest(mockGrammarPracticeParams());

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toContain('User not authenticated');
		});

		it('should return 401 when user ID is missing', async () => {
			mockAuth.mockResolvedValue({ user: {} } as any);
			const request = createRequest(mockGrammarPracticeParams());

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toContain('User not authenticated');
		});
	});

	describe('Input Validation', () => {
		it('should validate required keywords', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), keywords: [] };
			const request = createRequest(invalidBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('At least one keyword is required');
		});

		it('should validate maximum keywords', async () => {
			const invalidBody = {
				...mockGrammarPracticeParams(),
				keywords: Array(11).fill('keyword'),
			};
			const request = createRequest(invalidBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Cannot use more than 10 keywords');
		});

		it('should validate count range', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), count: 0 };
			const request = createRequest(invalidBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Count must be at least 1');
		});

		it('should validate maximum count', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), count: 6 };
			const request = createRequest(invalidBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Cannot generate more than 5 practice items');
		});

		it('should validate language enum', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), language: 'INVALID' };
			const request = createRequest(invalidBody);

			const response = await POST(request);

			expect(response.status).toBe(400);
		});

		it('should validate difficulty enum', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), difficulty: 'INVALID' };
			const request = createRequest(invalidBody);

			const response = await POST(request);

			expect(response.status).toBe(400);
		});

		it('should validate error density enum', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), errorDensity: 'invalid' };
			const request = createRequest(invalidBody);

			const response = await POST(request);

			expect(response.status).toBe(400);
		});

		it('should validate sentence count range', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), sentenceCount: 0 };
			const request = createRequest(invalidBody);

			const response = await POST(request);

			expect(response.status).toBe(400);
		});

		it('should validate maximum sentence count', async () => {
			const invalidBody = { ...mockGrammarPracticeParams(), sentenceCount: 31 };
			const request = createRequest(invalidBody);

			const response = await POST(request);

			expect(response.status).toBe(400);
		});
	});

	describe('Successful Generation', () => {
		it('should generate grammar practice successfully', async () => {
			const validBody = mockGrammarPracticeParams();
			const expectedResult = [mockGrammarPracticeResult()];
			
			mockLLMService.generateGrammarPractice.mockResolvedValue(expectedResult);
			const request = createRequest(validBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(expectedResult);
			expect(mockLLMService.generateGrammarPractice).toHaveBeenCalledWith({
				keywords: validBody.keywords,
				language: validBody.language,
				source_language: validBody.source_language,
				target_language: validBody.target_language,
				difficulty: validBody.difficulty,
				count: validBody.count,
				sentenceCount: validBody.sentenceCount,
				errorDensity: validBody.errorDensity,
			});
		});

		it('should handle optional parameters', async () => {
			const validBody = {
				keywords: ['test'],
				language: Language.EN,
				source_language: Language.VI,
				target_language: Language.EN,
				difficulty: Difficulty.BEGINNER,
				count: 1,
			};
			
			const expectedResult = [mockGrammarPracticeResult()];
			mockLLMService.generateGrammarPractice.mockResolvedValue(expectedResult);
			const request = createRequest(validBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(expectedResult);
			expect(mockLLMService.generateGrammarPractice).toHaveBeenCalledWith({
				...validBody,
				sentenceCount: undefined,
				errorDensity: undefined,
			});
		});
	});

	describe('Error Handling', () => {
		it('should handle LLM service errors', async () => {
			const validBody = mockGrammarPracticeParams();
			mockLLMService.generateGrammarPractice.mockRejectedValue(new Error('LLM Service Error'));
			const request = createRequest(validBody);

			const response = await POST(request);

			expect(response.status).toBe(500);
		});

		it('should handle malformed JSON', async () => {
			const request = {
				json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
			} as unknown as NextRequest;

			const response = await POST(request);

			expect(response.status).toBe(500);
		});
	});

	describe('Development Cache', () => {
		const originalNodeEnv = process.env.NODE_ENV;

		afterEach(() => {
			process.env.NODE_ENV = originalNodeEnv;
		});

		it('should attempt to read from cache in development mode', async () => {
			process.env.NODE_ENV = 'development';
			const fs = require('fs/promises');
			const cachedResult = [mockGrammarPracticeResult()];
			
			fs.readFile.mockResolvedValue(JSON.stringify(cachedResult));
			
			const validBody = mockGrammarPracticeParams();
			const request = createRequest(validBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(cachedResult);
			expect(mockLLMService.generateGrammarPractice).not.toHaveBeenCalled();
		});

		it('should generate and cache result when cache miss in development', async () => {
			process.env.NODE_ENV = 'development';
			const fs = require('fs/promises');
			const expectedResult = [mockGrammarPracticeResult()];
			
			fs.readFile.mockRejectedValue(new Error('File not found'));
			fs.mkdir.mockResolvedValue(undefined);
			fs.writeFile.mockResolvedValue(undefined);
			mockLLMService.generateGrammarPractice.mockResolvedValue(expectedResult);
			
			const validBody = mockGrammarPracticeParams();
			const request = createRequest(validBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(expectedResult);
			expect(mockLLMService.generateGrammarPractice).toHaveBeenCalled();
			expect(fs.writeFile).toHaveBeenCalled();
		});

		it('should not use cache in production mode', async () => {
			process.env.NODE_ENV = 'production';
			const fs = require('fs/promises');
			const expectedResult = [mockGrammarPracticeResult()];
			
			mockLLMService.generateGrammarPractice.mockResolvedValue(expectedResult);
			
			const validBody = mockGrammarPracticeParams();
			const request = createRequest(validBody);

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(expectedResult);
			expect(fs.readFile).not.toHaveBeenCalled();
			expect(fs.writeFile).not.toHaveBeenCalled();
		});
	});
});
